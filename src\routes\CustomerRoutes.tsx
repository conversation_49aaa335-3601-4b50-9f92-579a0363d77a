import AccountVerified from "@/features/auth-customer/pages/AccountVerified";
import ForgotPassword from "@/features/auth-customer/pages/ForgotPassword";
import Guardian from "@/features/auth-customer/pages/Guardian";
import Login from "@/features/auth-customer/pages/Login";
import PasswordReset from "@/features/auth-customer/pages/PasswordReset";
import Register from "@/features/auth-customer/pages/Register";
import SelectSports from "@/features/auth-customer/pages/SelectSports";
import VerifyEmail from "@/features/auth-customer/pages/VerifyEmail";
import { Route, Routes } from "react-router-dom";

function CustomerRoutes() {
  return (
    <Routes>
      <Route path="/register" element={<Register />} />
      <Route path="/verify-email" element={<VerifyEmail />} />
      <Route path="/account-verified" element={<AccountVerified />} />
      <Route path="/guardian-permission" element={<Guardian />} />
      <Route path="/select-sports" element={<SelectSports />} />
      <Route path="/login" element={<Login />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/password-reset" element={<PasswordReset />} />
    </Routes>
  );
}

export default CustomerRoutes;
