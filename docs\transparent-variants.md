# Transparent Variants for Input and Select Components

This document explains how to use the new "transparent" variants for the ShadCN Input and Select components.

## Overview

The transparent variants provide a clean, borderless appearance that's perfect for forms on colored backgrounds, overlays, or when you want a more minimal design aesthetic.

## Input Component

### Usage

```tsx
import { Input } from "@/components/ui/input";

// Default variant (existing behavior)
<Input placeholder="Default input" variant="default" />

// New transparent variant
<Input placeholder="Transparent input" variant="transparent" className="text-white" />
```

### Variants

- **`default`**: Standard input with border, background, and focus ring
- **`transparent`**: Borderless input with transparent background and no focus ring

### Styling Features

The transparent variant includes:
- No border (`border-0`)
- Transparent background (`bg-transparent`)
- No focus ring (`focus-visible:ring-0`)
- Semi-transparent white placeholder text (`placeholder:text-white/60`)

## Select Component

### Usage

```tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Default variant (existing behavior)
<Select>
  <SelectTrigger variant="default">
    <SelectValue placeholder="Default select" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>

// New transparent variant
<Select>
  <SelectTrigger variant="transparent" className="text-white">
    <SelectValue placeholder="Transparent select" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>
```

### Variants

- **`default`**: Standard select trigger with border, background, and focus ring
- **`transparent`**: Borderless select trigger with transparent background and no focus ring

### Styling Features

The transparent variant includes:
- No border (`border-0`)
- Transparent background (`bg-transparent`)
- No focus ring (`focus:ring-0`)
- Semi-transparent white placeholder text (`data-[placeholder]:text-white/60`)

## Best Practices

1. **Text Color**: When using transparent variants, always add appropriate text color classes (e.g., `className="text-white"`) to ensure visibility.

2. **Background Contrast**: Use transparent variants on backgrounds that provide sufficient contrast for readability.

3. **Form Styling**: Transparent variants work well in forms with colored backgrounds or overlay designs.

4. **Accessibility**: Ensure sufficient color contrast between text and background for accessibility compliance.

## Example Implementation

See `src/components/examples/TransparentVariantsExample.tsx` for a complete example showing both variants in action.

## Technical Implementation

The variants are implemented using `class-variance-authority` (cva) for consistent styling patterns:

- Input component: Uses `inputVariants` with default and transparent options
- Select component: Uses `selectTriggerVariants` with default and transparent options

Both components maintain full TypeScript support with proper variant prop typing.
