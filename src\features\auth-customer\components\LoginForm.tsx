import { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { LoginFormData } from "../type";
import { loginSchema } from "../validation";
import ShowPasswordIcon from "@/components/icons/showPasswordIcon";
import AppleIcon from "@/components/icons/appleIcon";
import { useNavigate } from "react-router-dom";
import GoogleIcon2 from "@/components/icons/googleUpdated";
import HidePasswordIcon from "@/components/icons/hidePasswordIcon";

function LoginForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<LoginFormData>({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const validateForm = (data: LoginFormData) => {
    const { error } = loginSchema.validate(data, { abortEarly: false });

    if (error) {
      // Clear previous errors
      clearErrors();

      // Set new errors
      error.details.forEach((detail: any) => {
        const path = detail.path.join(".");
        setError(path as keyof LoginFormData, {
          type: "validation",
          message: detail.message,
        });
      });
      return false;
    }
    return true;
  };

  const onSubmit = async (data: LoginFormData) => {
    if (!validateForm(data)) {
      return;
    }

    try {
      // Here you would typically send the data to your API
      console.log("Login form submitted:", data);

      // Simulate API call
      // navigate("/dashboard"); // or wherever you want to redirect after login
    } catch (error) {
      console.error("Login failed:", error);
      // Handle error (show error message, etc.)
    }
  };

  const handleGoogleLogin = () => {
    // Handle Google login
    console.log("Google login clicked");
  };

  const handleAppleLogin = () => {
    // Handle Apple login
    console.log("Apple login clicked");
  };

  const handleForgotPassword = () => {
    // Navigate to forgot password page
    console.log("Forgot password clicked");
    navigate("/forgot-password");
  };

  return (
    <div className="frosted-card w-full max-w-lg mx-auto rounded-[12px] lg:backdrop-blur-sm p-4 sm:px-6 ">
      {/* Header */}
      <div className="my-8 text-center">
        {/* Sports icon */}
        {/* <SportsIcon className="w-20 h-20 mx-auto mb-4 lg:w-16 lg:h-16" /> */}
        <h1 className="text-2.5xl font-bold text-white leading-tight">
          Let's Get You Back on <br /> Court
        </h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-5">
        {/* Email */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Email
          </label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="transparent"
                placeholder="<EMAIL>"
                className="text-white"
              />
            )}
          />
          {errors.email && (
            <p className="mt-1 text-xs text-red-300">{errors.email.message}</p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Password
          </label>
          <div className="relative">
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showPassword ? "text" : "password"}
                  variant="transparent"
                  placeholder="Enter your password"
                  className="pr-12 text-white"
                />
              )}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute transform -translate-y-1/2 right-3 top-1/2"
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <ShowPasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              ) : (
                <HidePasswordIcon className="w-5 h-5 text-gray-400 transition-colors hover:text-white" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-xs text-red-300">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Forgot Password Link */}
        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-white font-bold transition-colors hover:text-blue-300"
          >
            Forgot password?
          </button>
        </div>

        {/* Login Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant="white"
            size="lg"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? "Logging in..." : "Login"}
          </Button>
        </div>
      </form>

      {/* Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        <span className="px-4 text-sm text-[#FDFDFD]">Or continue with</span>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
      </div>

      {/* Social Login Buttons */}
      <div className="flex items-center justify-center gap-3">
        <button
          type="button"
          title="Google"
          onClick={handleGoogleLogin}
          className="flex w-[94px] h-[48px] rounded-[12px] items-center justify-center gap-2 bg-purple-900/80 border border-purple-700/50 text-white hover:bg-purple-900 hover:border-purple-600 transition-all duration-300 shadow-lg shadow-black/20"
        >
          <GoogleIcon2 />
        </button>

        <button
          type="button"
          title="Apple"
          onClick={handleAppleLogin}
          className="flex w-[94px] h-[48px] rounded-[12px] items-center justify-center gap-2 bg-purple-900/80 border border-purple-700/50 text-white hover:bg-purple-900 hover:border-purple-600 transition-all duration-300 shadow-lg shadow-black/20"
        >
          <AppleIcon />
        </button>
      </div>
    </div>
  );
}

export default LoginForm;
