import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import type { GuardianFormData } from "../type";
import { guardianSchema } from "../validation";
import { useNavigate } from "react-router-dom";
function GuardianForm() {
  const navigate = useNavigate();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<GuardianFormData>({
    defaultValues: {
      email: "",
      phone: "",
    },
  });

  const validateForm = (data: GuardianFormData) => {
    const { error } = guardianSchema.validate(data, { abortEarly: false });

    if (error) {
      // Clear previous errors
      clearErrors();

      // Set new errors
      error.details.forEach((detail) => {
        const path = detail.path.join(".");

        // Handle the "either email or phone" validation error
        if (detail.type === 'object.missing') {
          // Show error on both fields since user needs to provide at least one
          setError('email', {
            type: "validation",
            message: detail.message,
          });
          setError('phone', {
            type: "validation",
            message: detail.message,
          });
        } else {
          // Handle individual field validation errors
          setError(path as keyof GuardianFormData, {
            type: "validation",
            message: detail.message,
          });
        }
      });
      return false;
    }
    return true;
  };

  const onSubmit = async (data: GuardianFormData) => {
    if (!validateForm(data)) {
      return;
    }

    try {
      // Here you would typically send the data to your API
      console.log("Guardian form submitted:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Navigate to account verified page
      navigate("/select-sports");
    } catch (error) {
      console.error("Guardian invitation failed:", error);
      // Handle error (show error message, etc.)
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-4">
      {/* Parent's Email */}
      <div className="text-left">
        <label className="block mb-2 text-sm font-medium text-white">
          Parent's Email
        </label>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              type="email"
              variant="transparent"
              placeholder="<EMAIL>"
              className="text-white"
            />
          )}
        />
        {errors.email && (
          <p className="mt-1 text-xs text-red-300">{errors.email.message}</p>
        )}
      </div>

      {/* Or Divider */}
      <div className="flex items-center justify-center py-2">
        <span className="text-sm text-white/60">Or</span>
      </div>

      {/* Parent's Phone */}
      <div className="text-left">
        <label className="block mb-2 text-sm font-medium text-white">
          Parent's Phone
        </label>
        <Controller
          name="phone"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              type="tel"
              variant="transparent"
              placeholder="+56 8858 8858"
              className="text-white"
            />
          )}
        />
        {errors.phone && (
          <p className="mt-1 text-xs text-red-300">{errors.phone.message}</p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-4 pt-4 sm:flex-row">
        <Link
          to="/select-sports"
          className="flex-1 py-3 font-bold text-center text-white underline transition-colors hover:text-white/80"
        >
          Skip for now
        </Link>

        <Button
          type="submit"
          className="flex-1 text-blue-900"
          variant="white"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Inviting..." : "Invite Parent"}
        </Button>
      </div>
    </form>
  );
}

export default GuardianForm;
