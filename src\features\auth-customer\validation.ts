import Joi from "joi";

export const registerSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required().messages({
    "string.empty": "First name is required",
    "string.min": "First name must be at least 2 characters",
    "string.max": "First name cannot exceed 50 characters",
  }),

  lastName: Joi.string().min(2).max(50).required().messages({
    "string.empty": "Last name is required",
    "string.min": "Last name must be at least 2 characters",
    "string.max": "Last name cannot exceed 50 characters",
  }),

  sport: Joi.string().required().messages({
    "string.empty": "Please select your favorite sport",
  }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      "string.empty": "Email is required",
      "string.email": "Please enter a valid email address",
    }),

  phone: Joi.string()
    .pattern(/^[+]?[(]?[0-9]{1,4}[)]?[-\s./0-9]*$/)
    .min(7)
    .max(20)
    .required()
    .messages({
      "string.empty": "Phone number is required",
      "string.pattern.base": "Please enter a valid phone number",
      "string.min": "Phone number is too short",
      "string.max": "Phone number is too long",
    }),

  dateOfBirth: Joi.object({
    day: Joi.string()
      .pattern(/^(0[1-9]|[12][0-9]|3[01])$/)
      .required()
      .messages({
        "string.empty": "Day is required",
        "string.pattern.base": "Please enter a valid day (01-31)",
      }),

    month: Joi.string()
      .pattern(/^(0[1-9]|1[0-2])$/)
      .required()
      .messages({
        "string.empty": "Month is required",
        "string.pattern.base": "Please enter a valid month (01-12)",
      }),

    year: Joi.string()
      .pattern(/^(19|20)\d{2}$/)
      .required()
      .messages({
        "string.empty": "Year is required",
        "string.pattern.base": "Please enter a valid year (1900-2099)",
      }),
  }).required(),

  location: Joi.string().min(2).max(100).required().messages({
    "string.empty": "Location is required",
    "string.min": "Location must be at least 2 characters",
    "string.max": "Location cannot exceed 100 characters",
  }),

  zipCode: Joi.string()
    .pattern(/^[0-9]{4,10}$/)
    .required()
    .messages({
      "string.empty": "Zip code is required",
      "string.pattern.base": "Please enter a valid zip code (4-10 digits)",
    }),
});

export const guardianSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .allow("")
    .optional()
    .messages({
      "string.email": "Please enter a valid email address",
    }),

  phone: Joi.string()
    .allow("")
    .optional()
    .pattern(/^[+]?[(]?[0-9]{1,4}[)]?[-\s./0-9]*$/)
    .min(7)
    .max(20)
    .messages({
      "string.pattern.base": "Please enter a valid phone number",
      "string.min": "Phone number is too short",
      "string.max": "Phone number is too long",
    }),
}).or('email', 'phone').messages({
  'object.missing': 'Please provide either an email address or phone number',
});

export const loginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      "string.empty": "Email is required",
      "string.email": "Please enter a valid email address",
    }),

  password: Joi.string()
    .min(6)
    .required()
    .messages({
      "string.empty": "Password is required",
      "string.min": "Password must be at least 6 characters",
    }),
});

export const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      "string.empty": "Email is required",
      "string.email": "Please enter a valid email address",
    }),
});

export const passwordResetSchema = Joi.object({
  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .required()
    .messages({
      "string.empty": "Password is required",
      "string.min": "Password must be at least 8 characters",
      "string.pattern.base": "Password must contain at least one uppercase letter, one lowercase letter, and one number",
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      "string.empty": "Please confirm your password",
      "any.only": "Passwords do not match",
    }),
});
