# Sports Registration Form

This is a responsive sports registration form built with React, TypeScript, React Hook Form, and Joi validation.

## Features

- **Responsive Design**: Works perfectly on mobile, tablet, and desktop
- **Form Validation**: Comprehensive validation using Joi schemas
- **TypeScript**: Fully typed for better development experience
- **Transparent UI**: Uses transparent variants for inputs and selects
- **Sports Selection**: Dropdown with 25+ sports options
- **Date of Birth**: Separate inputs for day, month, and year
- **Real-time Validation**: Shows validation errors as user types

## Form Fields

1. **First Name** - Required, 2-50 characters
2. **Last Name** - Required, 2-50 characters  
3. **Favorite Sport** - Required, dropdown selection
4. **Email** - Required, valid email format
5. **Phone** - Required, valid phone number format
6. **Date of Birth** - Required, separate DD/MM/YYYY inputs
7. **Location** - Required, 2-100 characters
8. **Zip Code** - Required, 4-10 digits

## Validation Rules

- **Names**: Minimum 2 characters, maximum 50 characters
- **Email**: Must be valid email format
- **Phone**: Must match international phone number pattern
- **Date**: Day (01-31), Month (01-12), Year (1900-2099)
- **Location**: Minimum 2 characters, maximum 100 characters
- **Zip Code**: 4-10 digits only

## Components Used

- **Input**: ShadCN Input component with transparent variant
- **Select**: ShadCN Select component with transparent variant
- **Button**: ShadCN Button component with white variant
- **Form Handling**: React Hook Form with Controller
- **Validation**: Joi validation schema

## File Structure

```
src/features/auth-customer/
├── components/
│   └── RegisterForm.tsx          # Main form component
├── schemas/
│   └── registerSchema.ts         # Joi validation schema
├── data/
│   └── sports.ts                 # Sports options data
└── pages/
    └── Register.tsx              # Register page
```

## Usage

The form is automatically rendered in the Register page at the root route (`/`). The form includes:

- Gradient background (red to purple)
- Sports icon header
- Responsive grid layout
- Form validation with error messages
- Loading state during submission
- Success/error handling

## Styling

The form uses:
- **TailwindCSS** for styling
- **Transparent variants** for form inputs
- **White text** on gradient background
- **Responsive grid** for mobile/desktop layouts
- **Error messages** in red-300 color

## Form Submission

Currently, the form logs the data to console and shows a success alert. In production, you would:

1. Replace the console.log with an API call
2. Handle success/error states appropriately
3. Redirect user after successful registration
4. Show proper error messages for API failures
