import BrandLogo from "@/assets/logo.png";
import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import LoginForm from "../components/LoginForm";

function Login() {
  return (
    <CustomerAuthLayout>
      {/* Header */}
      <div className="flex-shrink-0 w-full px-4 py-6 sm:px-6 lg:px-10">
        <div className="flex items-center justify-between gap-4">
          <Link to={"/dashboard"}>
            <img src={BrandLogo} alt="ub sports" className="w-auto h-8" />
          </Link>
          <Link to="/register">
            <Button
              className="hidden px-6 text-center lg:block"
              variant={"default"}
            >
              Join UBsports
            </Button>
            <span className="block font-bold text-white lg:hidden">
              Join <PERSON>s
            </span>
          </Link>
        </div>
      </div>

      {/* Main Content - Centered */}
      <div className="flex items-center justify-center flex-1 px-4 pb-8 sm:px-6 lg:px-10">
        <div className="w-full max-w-lg">
          <LoginForm />
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default Login;
