// customer forgot password form
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { ForgotPasswordFormData } from "../type";
import { forgotPasswordSchema } from "../validation";
import BackArrowIcon from "@/components/icons/backArrowIcon";
import { useNavigate } from "react-router-dom";

function ForgotPasswordForm() {
  const navigate = useNavigate();
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<ForgotPasswordFormData>({
    defaultValues: {
      email: "",
    },
  });

  const validateForm = (data: ForgotPasswordFormData) => {
    const { error } = forgotPasswordSchema.validate(data, { abortEarly: false });

    if (error) {
      // Clear previous errors
      clearErrors();

      // Set new errors
      error.details.forEach((detail) => {
        const path = detail.path.join(".");
        setError(path as keyof ForgotPasswordFormData, {
          type: "validation",
          message: detail.message,
        });
      });
      return false;
    }
    return true;
  };

  const onSubmit = async (data: ForgotPasswordFormData) => {
    if (!validateForm(data)) {
      return;
    }
    try {
      // Here you would typically send the data to your API
      console.log("Forgot password form submitted:", data);

      // Simulate API call
      // After successful API call, you might navigate to a confirmation page
      navigate("/password-reset");
      
    } catch (error) {
      console.error("Forgot password request failed:", error);
      // Handle error (show error message, etc.)
    }
  };

  const handleGoBack = () => {
    navigate("/login");
  };

  return (
    <div className="frosted-card w-full max-w-lg mx-auto rounded-xl lg:backdrop-blur-sm p-4 sm:px-6">
      {/* Go Back Button */}
      <div className="mb-6">
        <button
          onClick={handleGoBack}
          className="flex gap-2 items-center text-white text-sm hover:text-gray-300 transition-colors"
        >
          <BackArrowIcon className="w-4 h-4 mr-2" />
          Go back
        </button>
      </div>

      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-white leading-tight">
          Verify email to reset <br/>password
        </h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full">
        {/* Email */}
        <div>
          <label className="block text-[16px] font-medium text-white mb-2">
            Email
          </label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="transparent"
                placeholder="<EMAIL>"
                className="text-white"
              />
            )}
          />
          {errors.email && (
            <p className="text-red-300 text-xs mt-1">{errors.email.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant="white"
            size="lg"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? "Sending..." : "Send verification link"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default ForgotPasswordForm;
