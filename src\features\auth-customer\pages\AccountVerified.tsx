import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import <PERSON><PERSON><PERSON> from "@/assets/logo.png";
import { Button } from "@/components/ui/button";
import VerfiedIcon from "@/components/icons/verfiedIcon";
import { Link, useNavigate } from "react-router-dom";
function AccountVerified() {
  const navigate = useNavigate();
const handleClick=()=>{
  const isMinor = localStorage.getItem("isMinor");
  if (isMinor === "true") {
    navigate("/guardian-permission");
  } else {
    navigate("/select-sports");
  }
}

  return (
    <CustomerAuthLayout>
      {/* Header */}
      <Link to='/dashboard' className="flex-shrink-0 w-full px-4 py-6 sm:px-6 lg:px-10">
        <img src={BrandLogo} alt="ub sports" className="w-auto h-8" />
      </Link>

      {/* Main Content - Centered */}
      <div className="flex items-center justify-center flex-1 px-4 pb-8 sm:px-6 lg:px-10">
        <div className="w-full max-w-lg p-8 mx-auto rounded-xl lg:backdrop-blur-2xl sm:p-10">
          <div className="flex flex-col items-center space-y-6 text-center">
            <VerfiedIcon className="w-16 h-16 sm:w-20 sm:h-20" />

            <h1 className="text-2.5xl font-bold text-white leading-tight">
              Account verified{" "}
            </h1>

            <p className="max-w-sm text-sm leading-relaxed text-white/80 sm:text-base">
              Your email has been confirmed. Welcome to the UB Sports universe!{" "}
            </p>

            <Button onClick={handleClick} className="w-full" variant="white">
              Get started
            </Button>
          </div>
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default AccountVerified;
