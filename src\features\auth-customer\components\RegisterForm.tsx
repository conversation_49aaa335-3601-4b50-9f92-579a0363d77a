// customer register form
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import type { RegisterFormData } from "../type";
import { registerSchema } from "../validation";
import { sportsOptions } from "../data/sports";
import SportsIcon from "@/components/icons/sportsIcon";
import { useNavigate } from "react-router-dom";
import { calculateAge } from "@/lib/utils";

function RegisterForm() {
  const navigate = useNavigate();
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
  } = useForm<RegisterFormData>({
    defaultValues: {
      firstName: "",
      lastName: "",
      sport: "",
      email: "",
      phone: "",
      dateOfBirth: {
        day: "",
        month: "",
        year: "",
      },
      location: "",
      zipCode: "",
    },
  });

  const validateForm = (data: RegisterFormData) => {
    const { error } = registerSchema.validate(data, { abortEarly: false });

    if (error) {
      // Clear previous errors
      clearErrors();

      // Set new errors
      error.details.forEach((detail) => {
        const path = detail.path.join(".");
        // Handle nested paths for dateOfBirth
        if (path.startsWith("dateOfBirth.")) {
          const subPath = path.replace("dateOfBirth.", "") as
            | "day"
            | "month"
            | "year";
          setError(`dateOfBirth.${subPath}` as const, {
            type: "validation",
            message: detail.message,
          });
        } else {
          setError(path as keyof RegisterFormData, {
            type: "validation",
            message: detail.message,
          });
        }
      });
      return false;
    }
    return true;
  };

  const onSubmit = async (data: RegisterFormData) => {
    if (!validateForm(data)) {
      return;
    }
    const age = calculateAge(data.dateOfBirth);
    if (age < 13) {
      localStorage.setItem("isMinor", "true");
    }
    try {
      // Here you would typically send the data to your API
      console.log("Form submitted:", data);

      // Simulate API call
      navigate("/verify-email");
    } catch (error) {
      console.error("Registration failed:", error);
      // Handle error (show error message, etc.)
    }
  };

  return (
    <div className="w-full max-w-lg p-4 mx-auto rounded-xl bg-container lg:backdrop-blur-sm sm:px-6">
      {/* Header */}
      <div className="mb-8 text-center">
        {/* Sports icon */}
        <SportsIcon className="w-20 h-20 mx-auto mb-4 lg:w-16 lg:h-16" />
        <h1 className="text-2.5xl font-bold text-white leading-tight">
          Start Your Sports <br className="lg:hidden" /> Journey
        </h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-5">
        {/* First Name & Last Name */}
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
          <div>
            <label className="block mb-2 text-sm font-medium text-white">
              First name
            </label>
            <Controller
              name="firstName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="John"
                  className="text-white"
                />
              )}
            />
            {errors.firstName && (
              <p className="mt-1 text-xs text-red-300">
                {errors.firstName.message}
              </p>
            )}
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-white">
              Last name
            </label>
            <Controller
              name="lastName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="Doe"
                  className="text-white"
                />
              )}
            />
            {errors.lastName && (
              <p className="mt-1 text-xs text-red-300">
                {errors.lastName.message}
              </p>
            )}
          </div>
        </div>

        {/* Sport Selection */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Select your favourite sport
          </label>
          <Controller
            name="sport"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger variant="transparent" className="text-white">
                  <SelectValue placeholder="Padel" />
                </SelectTrigger>
                <SelectContent>
                  {sportsOptions.map((sport) => (
                    <SelectItem key={sport.value} value={sport.value}>
                      {sport.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.sport && (
            <p className="mt-1 text-xs text-red-300">{errors.sport.message}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Email
          </label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                variant="transparent"
                placeholder="<EMAIL>"
                className="text-white"
              />
            )}
          />
          {errors.email && (
            <p className="mt-1 text-xs text-red-300">{errors.email.message}</p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Phone
          </label>
          <Controller
            name="phone"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="tel"
                variant="transparent"
                placeholder="+56 8858 8858"
                className="text-white"
              />
            )}
          />
          {errors.phone && (
            <p className="mt-1 text-xs text-red-300">{errors.phone.message}</p>
          )}
        </div>

        {/* Date of Birth */}
        <div>
          <label className="block mb-2 text-sm font-medium text-white">
            Date of birth
          </label>
          <div className="grid grid-cols-3 gap-2 sm:gap-3">
            <Controller
              name="dateOfBirth.day"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="DD"
                  maxLength={2}
                  className="text-center text-white"
                />
              )}
            />
            <Controller
              name="dateOfBirth.month"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="MM"
                  maxLength={2}
                  className="text-center text-white"
                />
              )}
            />
            <Controller
              name="dateOfBirth.year"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="YYYY"
                  maxLength={4}
                  className="text-center text-white"
                />
              )}
            />
          </div>
          {(errors.dateOfBirth?.day ||
            errors.dateOfBirth?.month ||
            errors.dateOfBirth?.year) && (
            <p className="mt-1 text-xs text-red-300">
              {errors.dateOfBirth?.day?.message ||
                errors.dateOfBirth?.month?.message ||
                errors.dateOfBirth?.year?.message}
            </p>
          )}
        </div>

        {/* Location and Zip Code */}
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
          <div>
            <label className="block mb-2 text-sm font-medium text-white">
              Location
            </label>
            <Controller
              name="location"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="Sydney, Australia"
                  className="text-white"
                />
              )}
            />
            {errors.location && (
              <p className="mt-1 text-xs text-red-300">
                {errors.location.message}
              </p>
            )}
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-white">
              Zip code
            </label>
            <Controller
              name="zipCode"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="transparent"
                  placeholder="6805548"
                  className="text-white"
                />
              )}
            />
            {errors.zipCode && (
              <p className="mt-1 text-xs text-red-300">
                {errors.zipCode.message}
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant="white"
            size="lg"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? "Joining..." : "Join"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default RegisterForm;
